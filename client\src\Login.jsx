import React, { useState } from 'react';
import axios from 'axios';
import swal from 'sweetalert';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './components/ui/card';
import { Input } from './components/ui/input';
import { Button } from './components/ui/button';
import { LockKeyhole, User } from 'lucide-react';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await axios.post('http://localhost:3000/api/users/login', { username, password });

      // Save the token to localStorage or cookies
      localStorage.setItem('token', response.data.token);

      // Display success message using swal
      swal("Login Successful", response.data.message, "success").then(() => {
        // Navigate to the home page after swal is dismissed
        navigate('/');
      });
    } catch (err) {
      swal("Login Failed", err.response?.data?.message || 'An error occurred', "error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="relative flex items-center justify-center min-h-screen overflow-hidden">
      {/* Animated Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 animate-gradient-x"></div>

      {/* Floating Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-white/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-blue-300/20 rounded-full blur-2xl animate-bounce"></div>
        <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-purple-300/15 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 right-1/3 w-64 h-64 bg-pink-300/20 rounded-full blur-xl animate-bounce delay-500"></div>
      </div>

      {/* Glassmorphism Login Card */}
      <Card className="relative w-[400px] backdrop-blur-xl bg-white/20 border border-white/30 shadow-2xl shadow-black/10">
        <div className="absolute inset-0 bg-gradient-to-br from-white/25 to-white/5 rounded-lg"></div>

        <CardHeader className="relative space-y-1 text-center">
          <div className="mx-auto mb-4 p-3 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 w-fit">
            <LockKeyhole className="h-8 w-8 text-white" />
          </div>
          <CardTitle className="text-3xl font-bold text-white drop-shadow-lg">
            Welcome Back
          </CardTitle>
          <CardDescription className="text-white/80 text-base">
            Enter your credentials to access your account
          </CardDescription>
        </CardHeader>

        <CardContent className="relative">
          <form onSubmit={handleLogin}>
            <div className="grid gap-6">
              <div className="grid gap-3">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-white/80" />
                  <label htmlFor="username" className="text-sm font-medium text-white/90">
                    Username
                  </label>
                </div>
                <Input
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  placeholder="Enter your username"
                  className="bg-white/20 backdrop-blur-sm border-white/30 text-white placeholder:text-white/60 focus:bg-white/25 focus:border-white/50 transition-all duration-300"
                />
              </div>

              <div className="grid gap-3">
                <div className="flex items-center gap-2">
                  <LockKeyhole className="h-4 w-4 text-white/80" />
                  <label htmlFor="password" className="text-sm font-medium text-white/90">
                    Password
                  </label>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  placeholder="Enter your password"
                  className="bg-white/20 backdrop-blur-sm border-white/30 text-white placeholder:text-white/60 focus:bg-white/25 focus:border-white/50 transition-all duration-300"
                />
              </div>

              <Button
                type="submit"
                disabled={loading}
                className="w-full bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/30 text-white font-semibold py-3 transition-all duration-300 hover:scale-105 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    Logging in...
                  </div>
                ) : (
                  'Sign In'
                )}
              </Button>
            </div>
          </form>
        </CardContent>

        <CardFooter className="relative flex justify-center">
          <p className="text-sm text-white/70 font-medium">
            Inventory Management System
          </p>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Login;
