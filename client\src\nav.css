/* slideNavbar.css */

/* General container */
.navbar-container {
  position: absolute;
  left: 0;
  top: 0;
  height: 100vh;
   border-radius: 15px; 
   margin: 5px; 
 position: fixed;
  width: 80px; /* Full width when open */
  transition: width 0.3s ease;
  background: #001427; /* Dark background */
 
 
}

.navbar-container.active{
  width: 250px;
}

/* .navbar-container.closed {
  width: 4.5rem;
} */

.navbar-container.active ~ .content{
  left: 250px;
  width: calc(100% - 250px);
}

.content{
  position: relative;
  min-height: 100vh;
  top: 0;
  left: 80px;
  width: calc(100% - 80px);
  transition: all .3s ease;
}

/* Button to toggle open/close */
.toggle-btn {
  background: none;
  border: none;
  color: white;
  padding: 1rem;
  cursor: pointer;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  margin-left: 10px;
  border-radius: 15px;
}

.navbar {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-top: 2rem; /* Space between toggle button and links */
  margin-left: 2px;
  margin-right: 3px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 1rem;
  color: white;
  text-decoration: none;
  font-size: 1rem;
  transition: background-color 0.3s ease;
  border-radius: 20px;
  margin-right: 3px;
 
  
}

.nav-link:hover, .toggle-btn:hover {
  background-color: #00406b; /* Darker hover */
}

.nav-icon {
  margin-right: 1rem; /* Space between icon and text */
  width: 1.5rem;
  height: 1.5rem;
  margin-left: 5px;
}

/* Hide text when closed */
.navbar-container.closed .nav-link span {
  display: none;
}

/* When open, the text will be shown */
.navbar-container.open .nav-link span {
  display: inline;
}
