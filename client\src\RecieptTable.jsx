import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import axios from 'axios';
import swal from 'sweetalert';
import { format } from 'date-fns';
import { Printer, ArrowLeft, Receipt, Store, Calendar, User, ShoppingCart } from 'lucide-react';
import { Button } from './components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './components/ui/card';
import './RTable.css';

const RecieptTable = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const data = location.state || {};

    const checkSaleExists = async (saleid) => {
        try {
            const response = await axios.get(`http://localhost:3000/api/sales/${saleid}`);
            return response.data; // Return the existing sale record if found
        } catch (error) {
            if (error.response && error.response.status === 404) {
                // Sale not found
                return null;
            }
            console.error('Error checking sale existence:', error);
            throw error; // Rethrow error for handling in the main function
        }
    };

    const handleSaveAndPrint = async () => {
        try {
            // Check if the sale record already exists
            const existingSale = await checkSaleExists(data.saleid);

            if (existingSale) {
                // If the record exists, just print it
               
                 window.print();
            } else {
                // If the record does not exist, save the sale data to the sales collection
                await axios.post('http://localhost:3000/api/sales', data);

                // Update the stock for each item in the sale
                for (const item of data.items) {
                    const productId = item.productId;
                    await axios.patch(`http://localhost:3000/api/products/${productId}`, {
                        quantitySold: item.quantity
                    });
                }

                // Show success alert
                swal({
                    title: "Success!",
                    text: "Data successfully saved to the database and stock updated.",
                    icon: "success",
                    button: "OK",
                }).then(() => {
                    // Print the receipt after alert is closed
                    window.print();
                });
            }
        } catch (error) {
            // Show error alert if saving fails or fetching existing record fails
            swal({
                title: "Error",
                text: "Failed to process your request. Please try again.",
                icon: "error",
                button: "OK",
            });
            console.error('Error processing request:', error);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4 print:bg-white print:p-0">
            {/* Header with navigation - hidden when printing */}
            <div className="max-w-4xl mx-auto mb-6 print:hidden">
                <div className="flex items-center justify-between">
                    <Button
                        variant="outline"
                        onClick={() => navigate(-1)}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Back
                    </Button>
                    <h1 className="text-2xl font-bold text-slate-800">Receipt Preview</h1>
                    <Button
                        onClick={handleSaveAndPrint}
                        className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                    >
                        <Printer className="h-4 w-4" />
                        Print Receipt
                    </Button>
                </div>
            </div>

            {/* Receipt Preview Container */}
            <div className="max-w-md mx-auto">
                <Card className="shadow-2xl border-0 print:shadow-none print:border print:border-gray-300">
                    <CardHeader className="text-center pb-4 print:pb-2">
                        <div className="flex justify-center mb-3 print:mb-1">
                            <div className="p-3 bg-blue-100 rounded-full print:bg-transparent print:p-0">
                                <Store className="h-8 w-8 text-blue-600 print:h-6 print:w-6 print:text-black" />
                            </div>
                        </div>
                        <CardTitle className="text-xl font-bold text-slate-800 print:text-lg">
                            Inventory System
                        </CardTitle>
                        <div className="flex items-center justify-center gap-2 text-sm text-slate-600 print:text-black">
                            <Receipt className="h-4 w-4" />
                            <span>Sales Receipt</span>
                        </div>
                        <div className="w-full h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent mt-3 print:bg-black"></div>
                    </CardHeader>

                    <CardContent className="space-y-4 print:space-y-2">
                        {/* Receipt Information */}
                        <div className="space-y-3 print:space-y-1">
                            <div className="flex justify-between items-center py-1 border-b border-slate-100 print:border-black">
                                <span className="text-sm font-medium text-slate-600 print:text-black">Sale ID:</span>
                                <span className="font-mono text-sm font-semibold">{data.saleid}</span>
                            </div>

                            {data.date && (
                                <div className="flex justify-between items-center py-1 border-b border-slate-100 print:border-black">
                                    <div className="flex items-center gap-2">
                                        <Calendar className="h-4 w-4 text-slate-500 print:hidden" />
                                        <span className="text-sm font-medium text-slate-600 print:text-black">Date:</span>
                                    </div>
                                    <span className="text-sm">{format(new Date(data.date), 'MMM d, yyyy HH:mm')}</span>
                                </div>
                            )}

                            <div className="flex justify-between items-center py-1 border-b border-slate-100 print:border-black">
                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 text-slate-500 print:hidden" />
                                    <span className="text-sm font-medium text-slate-600 print:text-black">Customer:</span>
                                </div>
                                <span className="text-sm">{data.customername || 'Walk-in Customer'}</span>
                            </div>

                            <div className="flex justify-between items-center py-1 border-b border-slate-100 print:border-black">
                                <span className="text-sm font-medium text-slate-600 print:text-black">Salesperson:</span>
                                <span className="text-sm">{data.salesperson || 'N/A'}</span>
                            </div>
                        </div>

                        {/* Items Section */}
                        <div className="mt-6 print:mt-3">
                            <div className="flex items-center gap-2 mb-3 print:mb-1">
                                <ShoppingCart className="h-4 w-4 text-slate-600 print:hidden" />
                                <h3 className="font-semibold text-slate-800 print:text-black">Items Purchased</h3>
                            </div>

                            <div className="space-y-2 print:space-y-1">
                                {/* Items Header */}
                                <div className="grid grid-cols-4 gap-2 py-2 bg-slate-50 rounded-lg text-xs font-semibold text-slate-700 print:bg-gray-100 print:text-black print:rounded-none">
                                    <span>Product</span>
                                    <span className="text-right">Price</span>
                                    <span className="text-center">Qty</span>
                                    <span className="text-right">Total</span>
                                </div>

                                {/* Items List */}
                                {data.items && data.items.map((item, index) => (
                                    <div key={index} className="grid grid-cols-4 gap-2 py-2 border-b border-slate-100 text-sm print:border-black">
                                        <span className="font-medium truncate">{item.product}</span>
                                        <span className="text-right">₦{item.price.toLocaleString('en-NG')}</span>
                                        <span className="text-center">{item.quantity}</span>
                                        <span className="text-right font-semibold">₦{item.totalprice.toLocaleString('en-NG')}</span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Summary Section */}
                        <div className="mt-6 pt-4 border-t-2 border-slate-200 print:border-black print:mt-3 print:pt-2">
                            <div className="space-y-2 print:space-y-1">
                                <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium text-slate-600 print:text-black">Total Items:</span>
                                    <span className="font-semibold">{data.totalitems}</span>
                                </div>

                                <div className="flex justify-between items-center py-2 border-t border-slate-100 print:border-black">
                                    <span className="text-lg font-bold text-slate-800 print:text-black">Grand Total:</span>
                                    <span className="text-lg font-bold text-green-600 print:text-black">
                                        ₦{data.grandtotal?.toLocaleString('en-NG') || '0'}
                                    </span>
                                </div>

                                <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium text-slate-600 print:text-black">Amount Paid:</span>
                                    <span className="font-semibold">₦{data.amountpaid?.toLocaleString('en-NG') || data.grandtotal?.toLocaleString('en-NG') || '0'}</span>
                                </div>

                                <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium text-slate-600 print:text-black">Balance:</span>
                                    <span className="font-semibold">₦{data.balance?.toLocaleString('en-NG') || '0'}</span>
                                </div>
                            </div>
                        </div>

                        {/* Footer */}
                        <div className="mt-6 pt-4 border-t border-slate-100 text-center print:border-black print:mt-3 print:pt-2">
                            <p className="text-sm text-slate-600 print:text-black">Thank you for your business!</p>
                            <p className="text-xs text-slate-500 mt-2 print:text-black print:mt-1">
                                Generated on {format(new Date(), 'MMM d, yyyy HH:mm')}
                            </p>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default RecieptTable;
