*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.page.card-1-page {
    
    height: 65vh;
    display: flex;
     justify-content: center;
    place-items: center;
    color: #f7f7f7;
    
   
    font-family: "Euclid Circular A", "Poppins";
  }
  
  h2,
  h3,
  h4 {
    margin: 0;
    font-weight: 500;
  }
  
  .cards {
    display: flex;
    gap: 30px;
  }
  
  .card-1-page .card {
    position: relative;
    perspective: 1000px;
    width: 300px;
    height: 300px;
  }
  
  .card-1-page .card header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    margin-bottom: 26px;
  }
  
  .card-1-page .card header h2 {
    font-size: 20px;
    text-transform: capitalize;
  }
  
  .card-1-page .card .front,
  .card-1-page .card .back {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    backface-visibility: hidden;
    background: #001629;
    border-radius: 10px;
    padding: 36px 36px 44px 44px;
    transition: 0.6s;
    cursor: pointer;
    flex-grow: 1;
    flex-basis: 200;
  }
  
  .card-1-page .back {
    transform: rotateY(180deg);
  }
  
  .card-1-page input {
    position: absolute;
    scale: 0;
  }
  
  .card-1-page input:checked ~ .card .back {
    transform: rotateY(0);
  }
  
  .card-1-page input:checked ~ .card .front {
    transform: rotateY(-180deg);
  }
  
  .card var {
    font-style: normal;
    font-size: 80px;
    line-height: 1;
  }
  
  .card h3 {
    margin: 0 0 30px;
    font-weight: 500;
  }
  
  #summary :is(var, h3) {
    color: #3b82f6;
  }
  
  #overdue :is(var, h3) {
    color: #e56363;
  }
  
  #features :is(var, h3) {
    color: #25b697;
  }
  
  .card :is(h4, p) {
    opacity: 0.6;
    font-size: 20px;
  }
  
  .card p {
    margin-top: 76px;
  }
  