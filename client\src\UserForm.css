/* UserForm.css */
/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 12px; /* Slim width */
}

::-webkit-scrollbar-track {
  background: #001e41; /* Light background color */
 /* Rounded corners for track */
}

::-webkit-scrollbar-thumb {
  background-color: #004e78; /* Light blue color */
  border-radius: 15px; /* Rounded corners for thumb */
  border: 1px solid #00aaff; /* Padding around thumb to match track */
}

/* Firefox */



.modal-content {
  max-width: 500px;
  overflow:auto;
  
  backdrop-filter: blur(70px);
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 0 10px rgb(0, 13, 128);
  background-color: rgba(2, 0, 29, 0.916);
}
.suk{
  background: none;
  box-shadow: none;
}


 
.form-container {
    max-width: 400px;
    margin: 30px auto;
    padding: 20px;
    border-radius: 15px;
    border: solid 1px rgb(0, 92, 163);
    max-height: 500px;
  
    
    justify-content: center;
   
}

.form-container h2 {
    text-align: center;
    color: #ffffff;
   background: #00254d;
   padding: 10px;
   border-radius: 10px;

   
}


.form-group {
    margin-bottom: 15px;
}

.form-group label {
    margin-bottom: 5px;
    color: #fffefe;
}

.form-group input ,select{
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    transition: border 0.3s;
    width: 100%;
    background-color: rgb(0, 0, 0);
    color: #f4f4f9;
    margin: 0px;
    
}

.form-group input:focus {
    border: 1px solid #007bff;
    outline: none;
}
/*
CSS @property and the New Style
https://ryanmulligan.dev/blog/css-property-new-style/
*/
@import url("https://fonts.googleapis.com/css2?family=Inter:opsz,wght@14..32,500&display=swap");

:root {
  --shiny-cta-bg: #000000;
  --shiny-cta-bg-subtle: #1a1818;
  --shiny-cta-fg: #ffffff;
  --shiny-cta-highlight: blue;
  --shiny-cta-highlight-subtle: #8484ff;
}

@property --gradient-angle {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}

@property --gradient-angle-offset {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}

@property --gradient-percent {
  syntax: "<percentage>";
  initial-value: 5%;
  inherits: false;
}

@property --gradient-shine {
  syntax: "<color>";
  initial-value: white;
  inherits: false;
}
.b{
  padding: 15px;
  border: 1px solid #0050b9;
  border-radius: 10px;
  margin: auto;
  background: #001f33;
  color: #ffffff;
  margin-left: 20px;
  transition: 0.3s;
  margin-right: 30px;
}
.b:hover{
  border-color: rgb(0, 85, 146);
  padding-right:10px;
  padding-left: 5px;
  background-color: #000204;
  color: rgb(0, 145, 255);
}




.submit-button {
    padding: 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #0f00b9;
}
