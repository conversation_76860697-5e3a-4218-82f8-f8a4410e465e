import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import swal from 'sweetalert'; // Import SweetAlert

const Admin = ({ children }) => {
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const verifyAdminAccess = async () => {
      const token = localStorage.getItem('token');

      if (!token) {
        swal("Access Denied", "No token found. Please login.", "error");
        navigate('/login');
        return;
      }

      try {
        const response = await axios.post(
          'http://localhost:3000/api/verify-admin',
          {}, // Empty body
          {
            headers: {
              Authorization: `Bearer ${token}`, // Send token as Bearer
            },
          }
        );

        if (response.status === 200) {
          setIsAuthorized(true); // User has admin/super admin access, allow rendering of the component
        }
      } catch (error) {
        const message = error.response?.data?.message || "An error occurred.";
        swal("Access Denied", message, "error"); // Show error message in SweetAlert
        navigate('/'); // Redirect to dashboard if not authorized or error occurs
      } finally {
        setLoading(false);
      }
    };

    verifyAdminAccess();
  }, [navigate]);

  // Show a loading message until verification is complete
  if (loading) {
    return <div>Loading...</div>;
  }

  // Render the children component only if the user has admin or super admin privileges
  return isAuthorized ? children : null;
};

export default Admin;
