import { useState, useEffect } from "react";
import {
  LayoutDashboard,
  CircleDollarSign,
  FileText,
  Users,
  Package,
  Menu,
  X,
  LogOut,
  BarChart3
} from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { <PERSON><PERSON> } from "./components/ui/button";
import { useNavigate } from "react-router-dom";
import swal from "sweetalert";

import { ThemeToggle } from "./components/theme-toggle";

// Utility function to decode JWT token and get user info
const getUserFromToken = () => {
  const token = localStorage.getItem('token');
  if (!token) return null;

  try {
    // Decode JWT token (simple base64 decode of payload)
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload;
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

export default function SlideNavbar() {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  // Check if screen is large (lg breakpoint and above)
  const [isLargeScreen, setIsLargeScreen] = useState(false);

  // Get user information from token
  const [userRole, setUserRole] = useState(null);
  const [userInfo, setUserInfo] = useState(null);

  // Get user information on component mount
  useEffect(() => {
    const user = getUserFromToken();
    if (user) {
      setUserRole(user.role);
      setUserInfo(user);
      console.log('User info detected:', user); // Debug log
    }
  }, []);

  // Effect to handle screen size changes
  useEffect(() => {
    const checkScreenSize = () => {
      setIsLargeScreen(window.innerWidth >= 1024); // lg breakpoint is 1024px
    };

    // Check initial screen size
    checkScreenSize();

    // Add event listener for window resize
    window.addEventListener('resize', checkScreenSize);

    // Cleanup event listener
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Determine if sidebar should be open (always open on large screens)
  const shouldBeOpen = isLargeScreen || isOpen;

  const toggleNavbar = () => {
    // Only allow toggle on small screens
    if (!isLargeScreen) {
      setIsOpen(!isOpen);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    swal("Logout Successful", "You have been logged out.", "success").then(() => {
      navigate('/login');
    });
  };

  const isActive = (path) => {
    return location.pathname === path;
  };

  // Define all navigation items with their required roles
  const allNavItems = [
    { path: "/", icon: <LayoutDashboard size={20} />, label: "Dashboard", roles: ["user", "admin"] },
    { path: "/reciept", icon: <FileText size={20} />, label: "Receipt", roles: ["user", "admin"] },
    { path: "/sales", icon: <BarChart3 size={20} />, label: "Sales", roles: ["admin"] },
    { path: "/products", icon: <Package size={20} />, label: "Products", roles: ["admin"] },
    { path: "/users", icon: <Users size={20} />, label: "Users", roles: ["admin"] },
  ];

  // Filter navigation items based on user role
  const navItems = userRole ? allNavItems.filter(item =>
    item.roles.includes(userRole)
  ) : [];

  return (
    <aside
      className={`fixed inset-y-0 left-0 z-50 flex flex-col bg-gradient-to-b from-slate-900 to-slate-800 border-r border-slate-700/50 shadow-xl transition-all duration-300 ${
        shouldBeOpen ? "w-64" : "w-20"
      }`}
    >
      <div className="flex items-center justify-between p-4 border-b border-slate-700/50">
        {shouldBeOpen && (
          <div className="flex items-center gap-2">
            <CircleDollarSign className="h-6 w-6 text-blue-400" />
            <h1 className="text-xl font-bold text-white">Inventory</h1>
          </div>
        )}
        {/* Only show toggle button on small screens */}
        {!isLargeScreen && (
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleNavbar}
            className="ml-auto text-slate-400 hover:text-white hover:bg-slate-700/50"
          >
            {shouldBeOpen ? <X size={20} /> : <Menu size={20} />}
          </Button>
        )}
      </div>

      {/* User Information Section */}
      {shouldBeOpen && (
        <div className="px-4 py-3 border-b border-slate-700/50 bg-slate-800/30">
          {userInfo ? (
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm shadow-lg">
                  {userInfo.username ? userInfo.username.charAt(0).toUpperCase() : 'U'}
                </div>
                {/* Online status indicator */}
                <div className="absolute -bottom-0.5 -right-0.5 h-3 w-3 bg-green-500 border-2 border-slate-800 rounded-full"></div>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {userInfo.username || 'Unknown User'}
                </p>
                <div className="flex items-center gap-2 mt-1">
                  <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                    userInfo.role === 'admin'
                      ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                      : 'bg-green-500/20 text-green-400 border border-green-500/30'
                  }`}>
                    {userInfo.role === 'admin' ? '👑 Admin' : '👤 User'}
                  </span>
                </div>
                <p className="text-xs text-slate-400 mt-1">
                  ID: {userInfo.id ? userInfo.id.slice(-6) : 'N/A'}
                </p>
              </div>
            </div>
          ) : (
            // Loading state
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-slate-700 animate-pulse"></div>
              <div className="flex-1">
                <div className="h-4 bg-slate-700 rounded animate-pulse mb-2"></div>
                <div className="h-3 bg-slate-700 rounded animate-pulse w-16"></div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Collapsed user info - just avatar */}
      {!shouldBeOpen && (
        <div className="px-2 py-3 border-b border-slate-700/50 bg-slate-800/30 flex justify-center">
          {userInfo ? (
            <div className="relative">
              <div
                className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-xs shadow-lg cursor-pointer hover:scale-105 transition-transform"
                title={`${userInfo.username} (${userInfo.role})`}
              >
                {userInfo.username ? userInfo.username.charAt(0).toUpperCase() : 'U'}
              </div>
              {/* Online status indicator */}
              <div className="absolute -bottom-0.5 -right-0.5 h-2.5 w-2.5 bg-green-500 border border-slate-800 rounded-full"></div>
            </div>
          ) : (
            <div className="h-8 w-8 rounded-full bg-slate-700 animate-pulse"></div>
          )}
        </div>
      )}

      <nav className="flex-1 py-4 px-2 space-y-1 overflow-y-auto">
        {userRole ? (
          navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`flex items-center gap-3 px-3 py-2.5 rounded-lg transition-colors ${
                isActive(item.path)
                  ? "bg-blue-600/20 text-blue-400"
                  : "text-slate-400 hover:bg-slate-800 hover:text-white"
              } ${!shouldBeOpen && "justify-center"}`}
            >
              {item.icon}
              {shouldBeOpen && <span>{item.label}</span>}
            </Link>
          ))
        ) : (
          // Loading state - show skeleton items
          shouldBeOpen ? (
            <div className="space-y-1">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center gap-3 px-3 py-2.5">
                  <div className="h-5 w-5 bg-slate-700 rounded animate-pulse"></div>
                  <div className="h-4 w-20 bg-slate-700 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-1">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex justify-center px-3 py-2.5">
                  <div className="h-5 w-5 bg-slate-700 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          )
        )}
      </nav>

      <div className="p-4 border-t border-slate-700/50 space-y-2">
        {/* Theme Toggle Button */}
        <div className={`flex items-center gap-3 px-3 py-2 rounded-lg ${!shouldBeOpen && "justify-center"}`}>
          <ThemeToggle
            variant="ghost"
            size="icon"
          />
          {shouldBeOpen && <span className="text-slate-400">Toggle Theme</span>}
        </div>

        {/* Logout Button */}
        <Button
          variant="ghost"
          onClick={handleLogout}
          className={`w-full flex items-center gap-3 text-red-400 hover:text-red-300 hover:bg-red-900/20 ${
            !shouldBeOpen && "justify-center"
          }`}
        >
          <LogOut size={20} />
          {shouldBeOpen && <span>Logout</span>}
        </Button>
      </div>
    </aside>
  );
}
