const express = require('express');
const router = express.Router();
const { Sales } = require('./models'); 

router.post('/sales', async (req, res) => {
    try {
      const sale = new Sales(req.body);
      const savedSale = await sale.save();
      res.status(201).json(savedSale);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  });
  // Get the next saleid by fetching the highest saleid and adding one
router.get('/sales/next-saleid', async (req, res) => {
  try {
      const highestSale = await Sales.findOne().sort({ saleid: -1 }).select('saleid');
      const nextSaleId = highestSale ? highestSale.saleid + 1 : 1;  // Start with 1 if no sales exist
      res.json({ nextSaleId });
  } catch (error) {
      res.status(500).json({ message: error.message });
  }
});

  // Get all sales
  router.get('/sales', async (req, res) => {
    try {
      const sales = await Sales.find();
      res.json(sales);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  
  router.put('/sales/:id', async (req, res) => {
    try {
      const updatedSale = await Sales.findByIdAndUpdate(req.params.id, req.body, {
        new: true
      });
      if (!updatedSale) return res.status(404).json({ message: 'Sale not found' });
      res.json(updatedSale);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  });
  router.get('/sales/:saleid', async (req, res) => {
    const { saleid } = req.params;

    try {
        // Find the sale by saleid
        const saleRecord = await Sales.findOne({ saleid: saleid });

        if (!saleRecord) {
            return res.status(404).json({ message: 'Sale not found' });
        }

        // Return the found sale record
        res.status(200).json(saleRecord);
    } catch (error) {
        console.error('Error retrieving sale record:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

  // Delete a sale by ID
  router.delete('/sales/:id', async (req, res) => {
    try {
      const deletedSale = await Sales.findByIdAndDelete(req.params.id);
      if (!deletedSale) return res.status(404).json({ message: 'Sale not found' });
      res.json({ message: 'Sale deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  
  // Dashboard statistics endpoint
  router.get('/dashboard/stats', async (req, res) => {
    try {
      const { User, Product, Sales } = require('./models');

      // Get current date for filtering today's data
      const today = new Date();
      const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

      // Get current month for filtering this month's data
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);

      // Get last month for comparison
      const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 1);

      // Count total products
      const totalProducts = await Product.countDocuments();

      // Count products added today
      const productsAddedToday = await Product.countDocuments({
        dateAdded: { $gte: startOfToday, $lt: endOfToday }
      });

      // Count total users
      const totalUsers = await User.countDocuments();

      // Get last user login (we'll use dateCreated as proxy since we don't track last login)
      const lastUser = await User.findOne().sort({ dateCreated: -1 });

      // Calculate total sales for this month
      const salesThisMonth = await Sales.aggregate([
        {
          $match: {
            date: { $gte: startOfMonth, $lt: endOfMonth }
          }
        },
        {
          $group: {
            _id: null,
            totalSales: { $sum: "$grandtotal" },
            totalProfit: { $sum: "$totalprofit" },
            salesCount: { $sum: 1 }
          }
        }
      ]);

      // Calculate total sales for last month
      const salesLastMonth = await Sales.aggregate([
        {
          $match: {
            date: { $gte: startOfLastMonth, $lt: endOfLastMonth }
          }
        },
        {
          $group: {
            _id: null,
            totalSales: { $sum: "$grandtotal" }
          }
        }
      ]);

      // Count sales today
      const salesToday = await Sales.countDocuments({
        date: { $gte: startOfToday, $lt: endOfToday }
      });

      // Calculate growth rate
      const thisMonthSales = salesThisMonth.length > 0 ? salesThisMonth[0].totalSales : 0;
      const lastMonthSales = salesLastMonth.length > 0 ? salesLastMonth[0].totalSales : 0;
      const growthRate = lastMonthSales > 0 ?
        ((thisMonthSales - lastMonthSales) / lastMonthSales * 100).toFixed(1) : 0;

      const stats = {
        products: {
          total: totalProducts,
          addedToday: productsAddedToday
        },
        users: {
          total: totalUsers,
          lastUserCreated: lastUser ? lastUser.dateCreated : null
        },
        sales: {
          totalThisMonth: thisMonthSales,
          totalProfit: salesThisMonth.length > 0 ? salesThisMonth[0].totalProfit : 0,
          salesToday: salesToday,
          growthRate: parseFloat(growthRate)
        }
      };

      res.json(stats);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      res.status(500).json({ message: error.message });
    }
  });

  module.exports = router;