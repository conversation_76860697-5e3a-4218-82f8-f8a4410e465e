{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.12", "@radix-ui/react-label": "^2.1.5", "@radix-ui/react-select": "^2.2.3", "@radix-ui/react-slot": "^1.2.1", "axios": "^1.7.7", "bootstrap": "^5.3.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.453.0", "react": "^18.3.1", "react-bootstrap": "^2.10.5", "react-data-table-component": "^7.6.2", "react-dom": "^18.3.1", "react-hook-form": "^7.56.2", "react-modal": "^3.16.1", "react-router-dom": "^6.27.0", "sweetalert": "^2.1.2", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.16", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "vite": "^5.4.8"}}