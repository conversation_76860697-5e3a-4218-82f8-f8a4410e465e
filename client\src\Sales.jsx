import { useEffect, useState } from 'react';
import axios from 'axios';
import { CalendarIcon, DollarSign, ShoppingCart, Search, Eye, Printer, X, User, Receipt } from 'lucide-react';
import SlideNavbar from './SlideInNavbar';
import { DataTable } from './components/ui/data-table';
import { Input } from './components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from './components/ui/card';
import { Label } from './components/ui/label';
import { Button } from './components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from './components/ui/dialog';
import { format } from 'date-fns';

const Sales = () => {
    const [salesData, setSalesData] = useState([]);
    const [filterText, setFilterText] = useState(''); // State for search text
    const [filteredSales, setFilteredSales] = useState([]); // State for filtered data
    const [startDate, setStartDate] = useState(''); // State for start date
    const [endDate, setEndDate] = useState(''); // State for end date
    const [totalProfit, setTotalProfit] = useState(0); // State for total profit
    const [selectedSale, setSelectedSale] = useState(null); // State for selected sale
    const [isModalOpen, setIsModalOpen] = useState(false); // State for modal visibility

    // Fetch sales data from the backend
    useEffect(() => {
        const fetchSalesData = async () => {
            try {
                const response = await axios.get('http://localhost:3000/api/sales');
                setSalesData(response.data);
            } catch (error) {
                console.error("Error fetching sales data:", error);
            }
        };

        fetchSalesData();
    }, []);

    // Filter sales data based on sale ID and date range
    useEffect(() => {
        const filteredData = salesData.filter(sale => {
            const saleDate = new Date(sale.date);
            const isWithinDateRange =
                (!startDate || saleDate >= new Date(startDate)) &&
                (!endDate || saleDate <= new Date(endDate));
            return sale.saleid.toString().includes(filterText) && isWithinDateRange;
        });
        setFilteredSales(filteredData);

        // Calculate total profit for the filtered data
        const total = filteredData.reduce((acc, sale) => acc + sale.totalprofit, 0);
        setTotalProfit(total);
    }, [filterText, startDate, endDate, salesData]);

    // Function to handle viewing a sale
    const handleViewSale = (sale) => {
        setSelectedSale(sale);
        setIsModalOpen(true);
    };

    // Function to handle closing the modal
    const handleCloseModal = () => {
        setIsModalOpen(false);
        setSelectedSale(null);
    };

    // Function to handle reprinting a receipt
    const handleReprintReceipt = () => {
        if (selectedSale) {
            // Create a new window for printing
            const printWindow = window.open('', '_blank');
            printWindow.document.write(generateReceiptHTML(selectedSale));
            printWindow.document.close();
            printWindow.print();
        }
    };

    // Function to generate receipt HTML for printing
    const generateReceiptHTML = (sale) => {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Receipt - ${sale.saleid}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .receipt { max-width: 400px; margin: 0 auto; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .company-name { font-size: 24px; font-weight: bold; }
                    .receipt-info { margin: 20px 0; }
                    .items-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    .items-table th { background-color: #f2f2f2; }
                    .totals { margin-top: 20px; }
                    .total-row { display: flex; justify-content: space-between; margin: 5px 0; }
                    .grand-total { font-weight: bold; font-size: 18px; border-top: 2px solid #000; padding-top: 10px; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                <div class="receipt">
                    <div class="header">
                        <div class="company-name">Inventory System</div>
                        <div>Receipt</div>
                    </div>

                    <div class="receipt-info">
                        <div><strong>Sale ID:</strong> ${sale.saleid}</div>
                        <div><strong>Date:</strong> ${format(new Date(sale.date), 'MMM d, yyyy HH:mm')}</div>
                        <div><strong>Customer:</strong> ${sale.customername || 'Walk-in Customer'}</div>
                        <div><strong>Salesperson:</strong> ${sale.salesperson || 'N/A'}</div>
                    </div>

                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Price</th>
                                <th>Qty</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sale.items ? sale.items.map(item => `
                                <tr>
                                    <td>${item.product}</td>
                                    <td>₦${item.price.toLocaleString('en-NG')}</td>
                                    <td>${item.quantity}</td>
                                    <td>₦${item.totalprice.toLocaleString('en-NG')}</td>
                                </tr>
                            `).join('') : '<tr><td colspan="4">No items available</td></tr>'}
                        </tbody>
                    </table>

                    <div class="totals">
                        <div class="total-row">
                            <span><strong>Grand Total:</strong></span>
                            <span>₦${sale.grandtotal.toLocaleString('en-NG')}</span>
                        </div>
                        <div class="total-row">
                            <span><strong>Amount Paid:</strong></span>
                            <span>₦${sale.amountpaid ? sale.amountpaid.toLocaleString('en-NG') : sale.grandtotal.toLocaleString('en-NG')}</span>
                        </div>
                        <div class="total-row">
                            <span><strong>Balance:</strong></span>
                            <span>₦${sale.balance ? sale.balance.toLocaleString('en-NG') : '0'}</span>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <p>Thank you for your business!</p>
                    </div>
                </div>
            </body>
            </html>
        `;
    };

    // Define the columns for the DataTable
    const columns = [
        {
            key: "saleid",
            header: "Sale ID",
            cell: (row) => <div className="font-medium">{row.saleid}</div>,
        },
        {
            key: "totalitems",
            header: "Total Items",
            cell: (row) => (
                <div className="flex items-center gap-2">
                    <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                    <span>{row.totalitems}</span>
                </div>
            ),
        },
        {
            key: "grandtotal",
            header: "Grand Total",
            cell: (row) => (
                <div className="font-medium text-green-500">
                    ₦{row.grandtotal.toLocaleString('en-NG', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
                </div>
            ),
        },
        {
            key: "totalprofit",
            header: "Profit",
            cell: (row) => (
                <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-green-500" />
                    <span className="font-medium">₦{row.totalprofit.toLocaleString('en-NG', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}</span>
                </div>
            ),
        },
        {
            key: "date",
            header: "Date",
            cell: (row) => (
                <div className="flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                    <span>{format(new Date(row.date), 'MMM d, yyyy')}</span>
                </div>
            ),
        },
        {
            key: "actions",
            header: "Actions",
            cell: (row) => (
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewSale(row)}
                    className="flex items-center gap-2 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
                >
                    <Eye className="h-4 w-4" />
                    View
                </Button>
            ),
        },
    ];

    return (
        <div className="flex h-screen ">
            <SlideNavbar />
            <main className="flex-1 overflow-auto transition-all duration-300 ml-20 lg:ml-64 p-6">
                <div className="flex flex-col gap-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                        <h1 className="text-3xl font-bold">Sales Data</h1>

                        {/* Total Profit Card */}
                        <Card className="bg-gradient-to-br from-green-900/50 to-green-800/30 border-green-700/30 shadow-lg">
                            <CardContent className="p-4 flex items-center gap-4">
                                <div className="p-3 rounded-full bg-green-500/20 border border-green-500/30">
                                    <DollarSign className="h-6 w-6 text-green-500" />
                                </div>
                                <div>
                                    <p className="text-sm text-muted-foreground">Total Profit</p>
                                    <h2 className="text-2xl font-bold text-green-500">₦{totalProfit.toLocaleString('en-NG', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}</h2>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Filters */}
                    <Card className="border-border/40 bg-card/30 backdrop-blur-sm">
                        <CardHeader>
                            <CardTitle className="text-lg">Filters</CardTitle>
                            <CardDescription>Filter sales by ID or date range</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-col md:flex-row gap-4">
                                <div className="relative flex-1">
                                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        type="text"
                                        placeholder="Search by Sale ID"
                                        value={filterText}
                                        onChange={e => setFilterText(e.target.value)}
                                        className="pl-8 bg-background"
                                    />
                                </div>

                                <div className="flex flex-col sm:flex-row gap-4">
                                    <div className="flex-1">
                                        <Label htmlFor="startDate" className="mb-2 block">Start Date</Label>
                                        <div className="relative">
                                            <CalendarIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                            <Input
                                                id="startDate"
                                                type="date"
                                                value={startDate}
                                                onChange={e => setStartDate(e.target.value)}
                                                className="pl-8 bg-background"
                                            />
                                        </div>
                                    </div>

                                    <div className="flex-1">
                                        <Label htmlFor="endDate" className="mb-2 block">End Date</Label>
                                        <div className="relative">
                                            <CalendarIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                            <Input
                                                id="endDate"
                                                type="date"
                                                value={endDate}
                                                onChange={e => setEndDate(e.target.value)}
                                                className="pl-8 bg-background"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Sales Table */}
                    <Card className="border-border/40 bg-card/30 backdrop-blur-sm">
                        <CardContent className="p-0">
                            <DataTable
                                columns={columns}
                                data={filteredSales}
                                title="Sales History"
                                searchPlaceholder="Search sales..."
                                searchField="saleid"
                            />
                        </CardContent>
                    </Card>
                </div>
            </main>

            {/* Sale Details Modal */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            <Receipt className="h-5 w-5" />
                            Sale Details - {selectedSale?.saleid}
                        </DialogTitle>
                    </DialogHeader>

                    {selectedSale && (
                        <div className="space-y-6">
                            {/* Sale Information */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Sale Information</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        <div className="flex justify-between">
                                            <span className="font-medium">Sale ID:</span>
                                            <span>{selectedSale.saleid}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Date:</span>
                                            <span>{format(new Date(selectedSale.date), 'MMM d, yyyy HH:mm')}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Customer:</span>
                                            <span>{selectedSale.customername || 'Walk-in Customer'}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Salesperson:</span>
                                            <div className="flex items-center gap-2">
                                                <User className="h-4 w-4" />
                                                <span>{selectedSale.salesperson || 'N/A'}</span>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Summary</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        <div className="flex justify-between">
                                            <span className="font-medium">Total Items:</span>
                                            <div className="flex items-center gap-2">
                                                <ShoppingCart className="h-4 w-4" />
                                                <span>{selectedSale.totalitems}</span>
                                            </div>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Grand Total:</span>
                                            <span className="text-green-600 font-semibold">
                                                ₦{selectedSale.grandtotal.toLocaleString('en-NG')}
                                            </span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Profit:</span>
                                            <span className="text-green-600 font-semibold">
                                                ₦{selectedSale.totalprofit.toLocaleString('en-NG')}
                                            </span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Amount Paid:</span>
                                            <span>₦{(selectedSale.amountpaid || selectedSale.grandtotal).toLocaleString('en-NG')}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Balance:</span>
                                            <span>₦{(selectedSale.balance || 0).toLocaleString('en-NG')}</span>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Items Table */}
                            {selectedSale.items && selectedSale.items.length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Items Sold</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="overflow-x-auto">
                                            <table className="w-full border-collapse">
                                                <thead>
                                                    <tr className="border-b">
                                                        <th className="text-left p-2 font-medium">Product</th>
                                                        <th className="text-right p-2 font-medium">Price</th>
                                                        <th className="text-center p-2 font-medium">Quantity</th>
                                                        <th className="text-right p-2 font-medium">Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {selectedSale.items.map((item, index) => (
                                                        <tr key={index} className="border-b hover:bg-muted/50">
                                                            <td className="p-2">{item.product}</td>
                                                            <td className="p-2 text-right">₦{item.price.toLocaleString('en-NG')}</td>
                                                            <td className="p-2 text-center">{item.quantity}</td>
                                                            <td className="p-2 text-right font-medium">₦{item.totalprice.toLocaleString('en-NG')}</td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Action Buttons */}
                            <div className="flex justify-end gap-3 pt-4 border-t">
                                <Button variant="outline" onClick={handleCloseModal}>
                                    <X className="h-4 w-4 mr-2" />
                                    Close
                                </Button>
                                <Button onClick={handleReprintReceipt} className="bg-blue-600 hover:bg-blue-700">
                                    <Printer className="h-4 w-4 mr-2" />
                                    Reprint Receipt
                                </Button>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default Sales;
