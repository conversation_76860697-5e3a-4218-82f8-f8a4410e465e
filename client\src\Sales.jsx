import { useEffect, useState } from 'react';
import axios from 'axios';
import { CalendarIcon, DollarSign, ShoppingCart, Search, Eye, Printer, X, User, Receipt } from 'lucide-react';
import SlideNavbar from './SlideInNavbar';
import { DataTable } from './components/ui/data-table';
import { Input } from './components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from './components/ui/card';
import { Label } from './components/ui/label';
import { Button } from './components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from './components/ui/dialog';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';

const Sales = () => {
    const navigate = useNavigate();
    const [salesData, setSalesData] = useState([]);
    const [filterText, setFilterText] = useState(''); // State for search text
    const [filteredSales, setFilteredSales] = useState([]); // State for filtered data
    const [startDate, setStartDate] = useState(''); // State for start date
    const [endDate, setEndDate] = useState(''); // State for end date
    const [totalProfit, setTotalProfit] = useState(0); // State for total profit
    const [totalSales, setTotalSales] = useState(0); // State for total sales
    const [totalItems, setTotalItems] = useState(0); // State for total items sold
    const [selectedSale, setSelectedSale] = useState(null); // State for selected sale
    const [isModalOpen, setIsModalOpen] = useState(false); // State for modal visibility

    // Fetch sales data from the backend
    useEffect(() => {
        const fetchSalesData = async () => {
            try {
                const response = await axios.get('http://localhost:3000/api/sales');
                setSalesData(response.data);
            } catch (error) {
                console.error("Error fetching sales data:", error);
            }
        };

        fetchSalesData();
    }, []);

    // Filter sales data based on sale ID and date range
    useEffect(() => {
        const filteredData = salesData.filter(sale => {
            const saleDate = new Date(sale.date);

            // Improve date filtering to handle full day ranges
            let isWithinDateRange = true;

            if (startDate) {
                const startDateTime = new Date(startDate);
                startDateTime.setHours(0, 0, 0, 0); // Start of day
                isWithinDateRange = isWithinDateRange && saleDate >= startDateTime;
            }

            if (endDate) {
                const endDateTime = new Date(endDate);
                endDateTime.setHours(23, 59, 59, 999); // End of day
                isWithinDateRange = isWithinDateRange && saleDate <= endDateTime;
            }

            // Filter by sale ID if provided
            const matchesSaleId = !filterText || sale.saleid.toString().includes(filterText);

            return matchesSaleId && isWithinDateRange;
        });

        setFilteredSales(filteredData);

        // Calculate totals for the filtered data
        const totalProfitAmount = filteredData.reduce((acc, sale) => acc + (sale.totalprofit || 0), 0);
        const totalSalesAmount = filteredData.reduce((acc, sale) => acc + (sale.grandtotal || 0), 0);
        const totalItemsCount = filteredData.reduce((acc, sale) => acc + (sale.totalitems || 0), 0);

        setTotalProfit(totalProfitAmount);
        setTotalSales(totalSalesAmount);
        setTotalItems(totalItemsCount);
    }, [filterText, startDate, endDate, salesData]);

    // Quick filter functions
    const setQuickFilter = (days) => {
        const today = new Date();
        const startDate = new Date();
        startDate.setDate(today.getDate() - days);

        setStartDate(startDate.toISOString().split('T')[0]);
        setEndDate(today.toISOString().split('T')[0]);
    };

    const setThisMonth = () => {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

        setStartDate(firstDay.toISOString().split('T')[0]);
        setEndDate(today.toISOString().split('T')[0]);
    };

    const setLastMonth = () => {
        const today = new Date();
        const firstDayLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastDayLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);

        setStartDate(firstDayLastMonth.toISOString().split('T')[0]);
        setEndDate(lastDayLastMonth.toISOString().split('T')[0]);
    };

    // Function to handle viewing a sale
    const handleViewSale = (sale) => {
        setSelectedSale(sale);
        setIsModalOpen(true);
    };

    // Function to handle closing the modal
    const handleCloseModal = () => {
        setIsModalOpen(false);
        setSelectedSale(null);
    };

    // Function to handle reprinting a receipt
    const handleReprintReceipt = () => {
        if (selectedSale) {
            // Navigate to the receipt-table page with the selected sale data
            navigate('/reciept-table', {
                state: {
                    saleid: selectedSale.saleid,
                    customername: selectedSale.customername || 'Walk-in Customer',
                    salesperson: selectedSale.salesperson || 'N/A',
                    items: selectedSale.items || [],
                    totalitems: selectedSale.totalitems,
                    grandtotal: selectedSale.grandtotal,
                    amountpaid: selectedSale.amountpaid || selectedSale.grandtotal,
                    balance: selectedSale.balance || 0,
                    date: selectedSale.date
                }
            });
            // Close the modal after navigating
            handleCloseModal();
        }
    };

    // Define the columns for the DataTable
    const columns = [
        {
            key: "saleid",
            header: "Sale ID",
            cell: (row) => <div className="font-medium">{row.saleid}</div>,
        },
        {
            key: "totalitems",
            header: "Total Items",
            cell: (row) => (
                <div className="flex items-center gap-2">
                    <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                    <span>{row.totalitems}</span>
                </div>
            ),
        },
        {
            key: "grandtotal",
            header: "Grand Total",
            cell: (row) => (
                <div className="font-medium text-green-500">
                    ₦{row.grandtotal.toLocaleString('en-NG', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
                </div>
            ),
        },
        {
            key: "totalprofit",
            header: "Profit",
            cell: (row) => (
                <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-green-500" />
                    <span className="font-medium">₦{row.totalprofit.toLocaleString('en-NG', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}</span>
                </div>
            ),
        },
        {
            key: "date",
            header: "Date",
            cell: (row) => (
                <div className="flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                    <span>{format(new Date(row.date), 'MMM d, yyyy')}</span>
                </div>
            ),
        },
        {
            key: "actions",
            header: "Actions",
            cell: (row) => (
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewSale(row)}
                    className="flex items-center gap-2 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
                >
                    <Eye className="h-4 w-4" />
                    View
                </Button>
            ),
        },
    ];

    return (
        <div className="flex h-screen ">
            <SlideNavbar />
            <main className="flex-1 overflow-auto transition-all duration-300 ml-20 lg:ml-64 p-6">
                <div className="flex flex-col gap-6">
                    <div className="flex flex-col gap-4">
                        <h1 className="text-3xl font-bold">Sales Data</h1>

                        {/* Summary Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            {/* Total Sales Card */}
                            <Card className="bg-gradient-to-br from-blue-900/50 to-blue-800/30 border-blue-700/30 shadow-lg">
                                <CardContent className="p-4 flex items-center gap-4">
                                    <div className="p-3 rounded-full bg-blue-500/20 border border-blue-500/30">
                                        <DollarSign className="h-6 w-6 text-blue-500" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-muted-foreground">Total Sales</p>
                                        <h2 className="text-2xl font-bold text-blue-500">₦{totalSales.toLocaleString('en-NG', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}</h2>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Total Profit Card */}
                            <Card className="bg-gradient-to-br from-green-900/50 to-green-800/30 border-green-700/30 shadow-lg">
                                <CardContent className="p-4 flex items-center gap-4">
                                    <div className="p-3 rounded-full bg-green-500/20 border border-green-500/30">
                                        <DollarSign className="h-6 w-6 text-green-500" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-muted-foreground">Total Profit</p>
                                        <h2 className="text-2xl font-bold text-green-500">₦{totalProfit.toLocaleString('en-NG', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}</h2>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Total Items Card */}
                            <Card className="bg-gradient-to-br from-purple-900/50 to-purple-800/30 border-purple-700/30 shadow-lg">
                                <CardContent className="p-4 flex items-center gap-4">
                                    <div className="p-3 rounded-full bg-purple-500/20 border border-purple-500/30">
                                        <ShoppingCart className="h-6 w-6 text-purple-500" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-muted-foreground">Items Sold</p>
                                        <h2 className="text-2xl font-bold text-purple-500">{totalItems.toLocaleString('en-NG')}</h2>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>

                    {/* Filters */}
                    <Card className="border-border/40 bg-card/30 backdrop-blur-sm">
                        <CardHeader>
                            <CardTitle className="text-lg flex items-center gap-2">
                                <Search className="h-5 w-5" />
                                Filters & Search
                            </CardTitle>
                            <CardDescription>
                                Filter sales by ID or date range. Totals update automatically based on your filters.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {/* Search by Sale ID */}
                                <div className="relative">
                                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        type="text"
                                        placeholder="Search by Sale ID (e.g., 12345)"
                                        value={filterText}
                                        onChange={e => setFilterText(e.target.value)}
                                        className="pl-8 bg-background"
                                    />
                                </div>

                                {/* Date Range Filters */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="startDate" className="mb-2 block text-sm font-medium">
                                            From Date
                                        </Label>
                                        <div className="relative">
                                            <CalendarIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                            <Input
                                                id="startDate"
                                                type="date"
                                                value={startDate}
                                                onChange={e => setStartDate(e.target.value)}
                                                className="pl-8 bg-background"
                                            />
                                        </div>
                                    </div>

                                    <div>
                                        <Label htmlFor="endDate" className="mb-2 block text-sm font-medium">
                                            To Date
                                        </Label>
                                        <div className="relative">
                                            <CalendarIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                            <Input
                                                id="endDate"
                                                type="date"
                                                value={endDate}
                                                onChange={e => setEndDate(e.target.value)}
                                                className="pl-8 bg-background"
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Quick Filter Buttons */}
                                <div>
                                    <Label className="mb-2 block text-sm font-medium">Quick Filters</Label>
                                    <div className="flex flex-wrap gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setQuickFilter(0)}
                                            className="text-xs"
                                        >
                                            Today
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setQuickFilter(7)}
                                            className="text-xs"
                                        >
                                            Last 7 Days
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setQuickFilter(30)}
                                            className="text-xs"
                                        >
                                            Last 30 Days
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={setThisMonth}
                                            className="text-xs"
                                        >
                                            This Month
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={setLastMonth}
                                            className="text-xs"
                                        >
                                            Last Month
                                        </Button>
                                    </div>
                                </div>

                                {/* Filter Summary */}
                                {(filterText || startDate || endDate) && (
                                    <div className="flex flex-wrap items-center gap-2 p-3 bg-muted/30 rounded-lg border">
                                        <span className="text-sm font-medium">Active Filters:</span>
                                        {filterText && (
                                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-500/20 text-blue-400 border border-blue-500/30">
                                                Sale ID: {filterText}
                                            </span>
                                        )}
                                        {startDate && (
                                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-500/20 text-green-400 border border-green-500/30">
                                                From: {format(new Date(startDate), 'MMM d, yyyy')}
                                            </span>
                                        )}
                                        {endDate && (
                                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-500/20 text-green-400 border border-green-500/30">
                                                To: {format(new Date(endDate), 'MMM d, yyyy')}
                                            </span>
                                        )}
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => {
                                                setFilterText('');
                                                setStartDate('');
                                                setEndDate('');
                                            }}
                                            className="text-xs h-6 px-2"
                                        >
                                            Clear All
                                        </Button>
                                    </div>
                                )}

                                {/* Results Summary */}
                                <div className="text-sm text-muted-foreground">
                                    Showing {filteredSales.length} of {salesData.length} sales
                                    {(startDate || endDate) && (
                                        <span>
                                            {startDate && endDate ? ` from ${format(new Date(startDate), 'MMM d')} to ${format(new Date(endDate), 'MMM d, yyyy')}` :
                                             startDate ? ` from ${format(new Date(startDate), 'MMM d, yyyy')}` :
                                             ` until ${format(new Date(endDate), 'MMM d, yyyy')}`}
                                        </span>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Sales Table */}
                    <Card className="border-border/40 bg-card/30 backdrop-blur-sm">
                        <CardContent className="p-0">
                            <DataTable
                                columns={columns}
                                data={filteredSales}
                                title="Sales History"
                                searchPlaceholder="Search sales..."
                                searchField="saleid"
                            />
                        </CardContent>
                    </Card>
                </div>
            </main>

            {/* Sale Details Modal */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            <Receipt className="h-5 w-5" />
                            Sale Details - {selectedSale?.saleid}
                        </DialogTitle>
                    </DialogHeader>

                    {selectedSale && (
                        <div className="space-y-6">
                            {/* Sale Information */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Sale Information</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        <div className="flex justify-between">
                                            <span className="font-medium">Sale ID:</span>
                                            <span>{selectedSale.saleid}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Date:</span>
                                            <span>{format(new Date(selectedSale.date), 'MMM d, yyyy HH:mm')}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Customer:</span>
                                            <span>{selectedSale.customername || 'Walk-in Customer'}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Salesperson:</span>
                                            <div className="flex items-center gap-2">
                                                <User className="h-4 w-4" />
                                                <span>{selectedSale.salesperson || 'N/A'}</span>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Summary</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        <div className="flex justify-between">
                                            <span className="font-medium">Total Items:</span>
                                            <div className="flex items-center gap-2">
                                                <ShoppingCart className="h-4 w-4" />
                                                <span>{selectedSale.totalitems}</span>
                                            </div>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Grand Total:</span>
                                            <span className="text-green-600 font-semibold">
                                                ₦{selectedSale.grandtotal.toLocaleString('en-NG')}
                                            </span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Profit:</span>
                                            <span className="text-green-600 font-semibold">
                                                ₦{selectedSale.totalprofit.toLocaleString('en-NG')}
                                            </span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Amount Paid:</span>
                                            <span>₦{(selectedSale.amountpaid || selectedSale.grandtotal).toLocaleString('en-NG')}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="font-medium">Balance:</span>
                                            <span>₦{(selectedSale.balance || 0).toLocaleString('en-NG')}</span>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Items Table */}
                            {selectedSale.items && selectedSale.items.length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Items Sold</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="overflow-x-auto">
                                            <table className="w-full border-collapse">
                                                <thead>
                                                    <tr className="border-b">
                                                        <th className="text-left p-2 font-medium">Product</th>
                                                        <th className="text-right p-2 font-medium">Price</th>
                                                        <th className="text-center p-2 font-medium">Quantity</th>
                                                        <th className="text-right p-2 font-medium">Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {selectedSale.items.map((item, index) => (
                                                        <tr key={index} className="border-b hover:bg-muted/50">
                                                            <td className="p-2">{item.product}</td>
                                                            <td className="p-2 text-right">₦{item.price.toLocaleString('en-NG')}</td>
                                                            <td className="p-2 text-center">{item.quantity}</td>
                                                            <td className="p-2 text-right font-medium">₦{item.totalprice.toLocaleString('en-NG')}</td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Action Buttons */}
                            <div className="flex justify-end gap-3 pt-4 border-t">
                                <Button variant="outline" onClick={handleCloseModal}>
                                    <X className="h-4 w-4 mr-2" />
                                    Close
                                </Button>
                                <Button onClick={handleReprintReceipt} className="bg-blue-600 hover:bg-blue-700">
                                    <Printer className="h-4 w-4 mr-2" />
                                    Reprint Receipt
                                </Button>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default Sales;
