@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    /* Dark mode colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Light mode background */
  body {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.dev/svgjs' width='1440' height='560' preserveAspectRatio='none' viewBox='0 0 1440 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1000%26quot%3b)' fill='none'%3e%3crect width='1440' height='560' x='0' y='0' fill='rgba(240, 240, 250, 0.5)'%3e%3c/rect%3e%3cpath d='M0%2c543.872C110.332%2c554.844%2c224.545%2c553.46%2c323.332%2c503.114C427.421%2c450.066%2c513.476%2c362.926%2c562.824%2c257.033C612.372%2c150.71%2c634.343%2c25.522%2c597.851%2c-85.958C563.254%2c-191.651%2c458.608%2c-252.249%2c372.914%2c-323.132C300.498%2c-383.032%2c223.987%2c-431.14%2c137.136%2c-467.041C40.842%2c-506.845%2c-60.949%2c-577.043%2c-159.501%2c-543.212C-258.146%2c-509.349%2c-269.992%2c-370.981%2c-347.141%2c-300.799C-440.146%2c-216.193%2c-627.622%2c-217.006%2c-653.371%2c-93.941C-678.55%2c26.401%2c-523.049%2c105.743%2c-453.4%2c207.061C-397.526%2c288.34%2c-363.663%2c383.853%2c-284.487%2c442.67C-202.02%2c503.931%2c-102.227%2c533.706%2c0%2c543.872' fill='rgba(230, 230, 250, 0.4)'%3e%3c/path%3e%3cpath d='M1440 860.95C1498.575 852.684 1555.8220000000001 848.262 1610.017 824.552 1672.377 797.27 1741.5520000000001 772.076 1776.566 713.705 1812.746 653.39 1810.317 577.811 1799.733 508.278 1789.209 439.142 1763.124 372.53700000000003 1716.6770000000001 320.25800000000004 1670.563 268.354 1607.807 234.88 1540.772 216.803 1474.739 198.99599999999998 1406.576 204.014 1339.761 218.61700000000002 1268.84 234.11700000000002 1191.387 248.51799999999997 1144.005 303.518 1096.657 358.477 1093.9180000000001 437.168 1089.12 509.551 1084.623 577.392 1092.004 644.267 1117.231 707.404 1143.407 772.915 1172.362 847.019 1236.068 877.325 1298.924 907.227 1371.077 870.677 1440 860.95' fill='rgba(220, 220, 250, 0.4)'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1000'%3e%3crect width='1440' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
    background-attachment: fixed;
    background-size: cover;
  }

  /* Dark mode background */
  .dark body {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.dev/svgjs' width='1440' height='560' preserveAspectRatio='none' viewBox='0 0 1440 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1077%26quot%3b)' fill='none'%3e%3crect width='1440' height='560' x='0' y='0' fill='rgba(14%2c 42%2c 71%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c543.872C110.332%2c554.844%2c224.545%2c553.46%2c323.332%2c503.114C427.421%2c450.066%2c513.476%2c362.926%2c562.824%2c257.033C612.372%2c150.71%2c634.343%2c25.522%2c597.851%2c-85.958C563.254%2c-191.651%2c458.608%2c-252.249%2c372.914%2c-323.132C300.498%2c-383.032%2c223.987%2c-431.14%2c137.136%2c-467.041C40.842%2c-506.845%2c-60.949%2c-577.043%2c-159.501%2c-543.212C-258.146%2c-509.349%2c-269.992%2c-370.981%2c-347.141%2c-300.799C-440.146%2c-216.193%2c-627.622%2c-217.006%2c-653.371%2c-93.941C-678.55%2c26.401%2c-523.049%2c105.743%2c-453.4%2c207.061C-397.526%2c288.34%2c-363.663%2c383.853%2c-284.487%2c442.67C-202.02%2c503.931%2c-102.227%2c533.706%2c0%2c543.872' fill='%230b223a'%3e%3c/path%3e%3cpath d='M1440 860.95C1498.575 852.684 1555.8220000000001 848.262 1610.017 824.552 1672.377 797.27 1741.5520000000001 772.076 1776.566 713.705 1812.746 653.39 1810.317 577.811 1799.733 508.278 1789.209 439.142 1763.124 372.53700000000003 1716.6770000000001 320.25800000000004 1670.563 268.354 1607.807 234.88 1540.772 216.803 1474.739 198.99599999999998 1406.576 204.014 1339.761 218.61700000000002 1268.84 234.11700000000002 1191.387 248.51799999999997 1144.005 303.518 1096.657 358.477 1093.9180000000001 437.168 1089.12 509.551 1084.623 577.392 1092.004 644.267 1117.231 707.404 1143.407 772.915 1172.362 847.019 1236.068 877.325 1298.924 907.227 1371.077 870.677 1440 860.95' fill='%23113255'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1077'%3e%3crect width='1440' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
    background-attachment: fixed;
    background-size: cover;
  }
}