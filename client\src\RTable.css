.receipt-container {
    max-width: 400px;
    margin: 20px auto;
    padding: 10px;
    border: 1px solid #000;
    background-color: #fff;
    font-family: Arial, sans-serif;
    font-size: small;
    
}
.store-title {
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 5px;
}

.receipt-header, .receipt-info {
    text-align: center;
    font-size: 12px;
    line-height: 1.2;
}

.receipt-info p, .receipt-header p {
    margin: 2px 0;
}
.receipt-info {
    
   
    display: flex;
}

.receipt-info p {
    margin: 5px 0;
}


.receipt-title {
    text-align: center;
    font-weight: bold;
    font-size: 14px;
    margin-top: 10px;
    border-top: 1px dashed #000;
    border-bottom: 1px dashed #000;
    padding: 5px 0;
    box-shadow: none;
    color: black;
    box-shadow: none;
    border-radius: 0px;
}
p {
    color: white;
}
.receipt-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    

}

.receipt-table th, .receipt-table td {
    border: 1px solid #000;
    padding: 5px;
    text-align: center;
    
    font-size: 11px;
    
} 


.receipt-table th {
    background-color: #f9f9f9;
}

.footer-label {
    text-align: right;
    padding-right: 10px;
    font-weight: bold;
}

.receipt-footer {
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    margin-top: 10px;
}

.powered-by {
    text-align: center;
    font-size: 10px;
    margin-top: 5px;
}
.print-button {
    margin-top: 20px;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
}

/* Hide the print button when printing */
@media print {
    .print-button {
        display: none;
    }
}

.print-button:hover {
    background-color: #45a049;
}
