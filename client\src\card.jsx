import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
    Users,
    Package,
    TrendingUp,
    DollarSign,
    ShoppingCart,
    BarChart3
} from "lucide-react";
import { Card as ShadcnCard, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './components/ui/card';

function Card() {
    const [dashboardStats, setDashboardStats] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Fetch dashboard statistics
    useEffect(() => {
        const fetchDashboardStats = async () => {
            try {
                setLoading(true);
                const response = await axios.get('http://localhost:3000/api/dashboard/stats');
                setDashboardStats(response.data);
                setError(null);
            } catch (error) {
                console.error("Error fetching dashboard stats:", error);
                setError("Failed to load dashboard data");
            } finally {
                setLoading(false);
            }
        };

        fetchDashboardStats();
    }, []);

    // Format currency to Naira
    const formatNaira = (amount) => {
        return new Intl.NumberFormat('en-NG', {
            style: 'currency',
            currency: 'NGN',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    };

    // Format date for display
    const formatDate = (dateString) => {
        if (!dateString) return 'Never';
        const date = new Date(dateString);
        const today = new Date();
        const diffTime = Math.abs(today - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) return 'Today';
        if (diffDays === 2) return 'Yesterday';
        if (diffDays <= 7) return `${diffDays - 1} days ago`;
        return date.toLocaleDateString();
    };

    if (loading) {
        return (
            <div className="p-6">
                <h2 className="text-3xl font-bold mb-6 text-white">Dashboard</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {[1, 2, 3, 4].map((i) => (
                        <ShadcnCard key={i} className="border border-white/10 shadow-lg animate-pulse">
                            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
                                <div className="h-6 bg-gray-300 rounded w-20"></div>
                                <div className="h-8 w-8 bg-gray-300 rounded-full"></div>
                            </CardHeader>
                            <CardContent>
                                <div className="h-8 bg-gray-300 rounded w-16 mb-2"></div>
                                <div className="h-4 bg-gray-300 rounded w-32"></div>
                            </CardContent>
                            <CardFooter>
                                <div className="h-3 bg-gray-300 rounded w-24"></div>
                            </CardFooter>
                        </ShadcnCard>
                    ))}
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="p-6">
                <h2 className="text-3xl font-bold mb-6 text-white">Dashboard</h2>
                <div className="text-red-500 text-center">{error}</div>
            </div>
        );
    }

    const cards = [
        {
          name: "Products",
          total: dashboardStats?.products?.total || 0,
          description: "Total products in inventory",
          footer: `${dashboardStats?.products?.addedToday || 0} added today`,
          icon: <Package className="h-8 w-8 text-blue-500" />,
          color: "bg-gradient-to-br from-blue-900/50 to-blue-800/30 hover:from-blue-800/50 hover:to-blue-700/30"
        },
        {
          name: "Users",
          total: dashboardStats?.users?.total || 0,
          description: "Active system users",
          footer: `Last user: ${formatDate(dashboardStats?.users?.lastUserCreated)}`,
          icon: <Users className="h-8 w-8 text-green-500" />,
          color: "bg-gradient-to-br from-green-900/50 to-green-800/30 hover:from-green-800/50 hover:to-green-700/30"
        },
        {
          name: "Sales",
          total: formatNaira(dashboardStats?.sales?.totalThisMonth || 0),
          description: "Total sales this month",
          footer: `${dashboardStats?.sales?.salesToday || 0} sales today`,
          icon: <DollarSign className="h-8 w-8 text-yellow-500" />,
          color: "bg-gradient-to-br from-yellow-900/50 to-yellow-800/30 hover:from-yellow-800/50 hover:to-yellow-700/30"
        },
        {
          name: "Analytics",
          total: `${dashboardStats?.sales?.growthRate || 0}%`,
          description: "Growth rate",
          footer: "Compared to last month",
          icon: <TrendingUp className="h-8 w-8 text-purple-500" />,
          color: "bg-gradient-to-br from-purple-900/50 to-purple-800/30 hover:from-purple-800/50 hover:to-purple-700/30"
        },
    ];

    return (
        <div className="p-6">
            <h2 className="text-3xl font-bold mb-6 text-white">Dashboard</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {cards.map((card) => (
                    <ShadcnCard
                        key={card.name}
                        className={`border border-white/10 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 ${card.color}`}
                    >
                        <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
                            <CardTitle className="text-xl font-bold">{card.name}</CardTitle>
                            <div className="p-2 rounded-full bg-background/10">
                                {card.icon}
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold">{card.total}</div>
                            <p className="text-sm text-muted-foreground mt-1">{card.description}</p>
                        </CardContent>
                        <CardFooter>
                            <p className="text-xs text-muted-foreground flex items-center gap-1">
                                <BarChart3 className="h-3 w-3" />
                                {card.footer}
                            </p>
                        </CardFooter>
                    </ShadcnCard>
                ))}
            </div>
        </div>
    );
}

export default Card;
